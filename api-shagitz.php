<?php
header('Content-Type: application/json');

if (!isset($_SERVER['REQUEST_METHOD']) || $_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed. Only POST requests are accepted.']);
    exit;
}

if (!isset($_POST['player_tag'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Player tag is required.']);
    exit;
}

$playerTag = $_POST['player_tag'];

// Remove the '#' symbol if present and add it back
$playerTag = str_replace('#', '', $playerTag);
$playerTag = '#' . strtoupper($playerTag);

// New API URL - uses GET method with player tag in URL
$targetApiUrl = 'https://clashofclanevents.duckdns.org/api/player/' . urlencode($playerTag);

$ch = curl_init();

curl_setopt($ch, CURLOPT_URL, $targetApiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'User-Agent: ClashOfClansApp/1.0'
]);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (curl_errno($ch)) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Proxy Error',
        'reason' => 'Failed to connect to the target API: ' . curl_error($ch)
    ]);
    curl_close($ch);
    exit;
}

curl_close($ch);

// Simple error handling
if ($httpCode !== 200) {
    http_response_code($httpCode);
    echo json_encode([
        'error' => 'API Error',
        'reason' => 'Failed to fetch player data. Please check the player tag and try again.',
        'code' => $httpCode
    ]);
    exit;
}

http_response_code(200);
echo $response;

?>