$(document).ready(function() {
    const rewards = [
        {
            title: "Cyclone Prince",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/cyclone_prince.png"
        },
        {
            title: "Clashmania Scenery",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/clashmania_scenery.png"
        },
        {
            title: "Clash of Dragon",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/clash_of_dragon.png"
        },
		{
            title: "Darkness Warden",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/darkness_warden.png"
        },
		{
            title: "Grand Monk",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/grand_monk.png"
        },
		{
            title: "High Seas Scenery",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/high_seas_scenery.png"
        },
		{
            title: "King <PERSON>",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/king_cody.png"
        },
		{
            title: "Kyudo Queen",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/kyudo_queen.png"
        },
		{
            title: "Royal Ninja",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/royal_ninja.png"
        },
		{
            title: "Samurai King",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/samurai_king.png"
        },
		{
            title: "The Royalest",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/the_royalest.png"
        },
		{
            title: "War Machine Scenery",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/war_machine_scenery.png"
        },
		{
            title: "Year Of Snake",
            imageSrc: "https://cdn.shagitzsan.my.id/coc/year_of_snake.png"
        }
    ];

    const randomIndex = Math.floor(Math.random() * rewards.length);
    const selectedReward = rewards[randomIndex];

    const $gImgBox = $('.g-imgBox');
    $gImgBox.find('.g-title').text(selectedReward.title);
    $gImgBox.find('img').attr('src', selectedReward.imageSrc);

    var buka = new Audio('media/shagitz.mp3');
    function playSound() {
        if (typeof buka !== 'undefined' && buka.play) {
            buka.play().catch(function(error) {});
        }
    }

    $('#submitButton').on('click', function(event) {
        playSound();

        const playerTagInput = $('#playertag');
        let playerTag = playerTagInput.val().trim();
        const formMessage = $('.id-alert');
        const loadingSpinner = $('#loadingSpinner');
        const submitButton = $(this);

        formMessage.hide().text('').removeClass('error success');

        if (!playerTag) {
            formMessage.text('Player ID cannot be empty.').addClass('error').show();
            playerTagInput.focus();
            return;
        }

        $.ajax({
            url: 'api-shagitz.php',
            type: 'POST',
            data: { player_tag: playerTag },
            dataType: 'json',
            beforeSend: function() {
                loadingSpinner.show();
                submitButton.prop('disabled', true).text('Checking...');
            },
            success: function(response) {
                // Store response data globally for later use
                window.playerData = response;

                // Debug: Log the response to see what we're getting
                console.log('API Response:', response);
                console.log('Clan Data:', response.clan);

                // Show player confirmation card instead of hiding verification
                $('.player-confirmation-card').show();

                // Populate confirmation card with player data
                $('.player-name').text(response && response.name ? response.name : 'Unknown Player');
                $('.player-tag').text(response && response.tag ? response.tag : `#${playerTag.toUpperCase()}`);

                // Experience Level
                $('.exp-level').text(response && typeof response.expLevel !== 'undefined' ? `Level ${response.expLevel}` : 'N/A');

                // Town Hall Level
                if (response && typeof response.townHallLevel !== 'undefined' && response.townHallLevel > 0) {
                    const townHallImageUrl = `img/th/th${response.townHallLevel}.webp`;
                    $('.th-image').attr('src', townHallImageUrl).attr('alt', `Town Hall ${response.townHallLevel}`);
                    $('.th-level').text(`TH ${response.townHallLevel}`);
                } else {
                    $('.th-image').attr('src', 'img/th/th_default.webp').attr('alt', 'Town Hall');
                    $('.th-level').text('N/A');
                }

                // Trophies
                $('.trophy-count').text(response && typeof response.trophies !== 'undefined' ? response.trophies : 'N/A');

                // Clan Information
                console.log('Checking clan data:', response.clan);
                console.log('Clan badge URL:', response.clan ? response.clan.badge : 'No clan object');
                console.log('Clan name:', response.clan ? response.clan.name : 'No clan object');

                if (response && response.clan && response.clan.badge && response.clan.name) {
                    console.log('Using clan data from API');
                    $('.clan-badge').attr('src', response.clan.badge).attr('alt', 'Clan Badge');
                    $('.clan-name').text(response.clan.name);
                } else {
                    console.log('Using default clan data');
                    $('.clan-badge').attr('src', 'img/default_clan_badge.png').attr('alt', 'Clan Badge');
                    $('.clan-name').text('No Clan');
                }

                // Prepare hidden form data for later use
                let townhallText = response && typeof response.townHallLevel !== 'undefined' && response.townHallLevel > 0 ? `Townhall ${response.townHallLevel}` : 'N/A';
                let expText = response && typeof response.expLevel !== 'undefined' ? `Exp Level ${response.expLevel}` : 'N/A';
                let apiPlayerTag = response && response.tag ? response.tag : `#${playerTag.toUpperCase()}`;
                let apiPlayerName = response && response.name ? response.name : '';
                let apiClanName = response && response.clan && response.clan.name ? response.clan.name : 'N/A';

                $('#s1_townhall').val(townhallText);
                $('#s1_exp').val(expText);
                $('#s1_playertag').val(apiPlayerTag);
                $('#s1_playername').val(apiPlayerName);
                $('#s1_clan').val(apiClanName);

                $('#s2_townhall').val(townhallText);
                $('#s2_exp').val(expText);
                $('#s2_playertag').val(apiPlayerTag);
                $('#s2_playername').val(apiPlayerName);
                $('#s2_clan').val(apiClanName);

                $('#s3_townhall').val(townhallText);
                $('#s3_exp').val(expText);
                $('#s3_playertag').val(apiPlayerTag);
                $('#s3_playername').val(apiPlayerName);
                $('#s3_clan').val(apiClanName);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                let errorMessage = `Error: ${errorThrown} (${jqXHR.status}). Unable to connect to server.`;
                try {
                    var errorResponse = JSON.parse(jqXHR.responseText);
                    if (errorResponse && errorResponse.reason === "notFound") {
                        errorMessage = 'Invalid Tag, Try again';
                    } else if (errorResponse && errorResponse.error) {
                        errorMessage = 'Invalid Tag or Server Error, Try again';
                    }
                } catch (e) {
                    console.warn("Error parsing server response or other error:", e);
                }

                formMessage.text(errorMessage).addClass('error').show();
            },
            complete: function() {
                loadingSpinner.hide();
                submitButton.prop('disabled', false).text('Search');
            }
        });
    });

    // Handle account confirmation
    $('#confirmAccount').on('click', function() {
        playSound();

        // Hide verification section and show account info
        $('.id-verification').hide();
        $('.accountInfo').show();

        // Populate the main account info section with stored data
        const response = window.playerData;
        $('.nickname').html(response && response.name ? `${response.name}` : '');
        $('#nicksuccess').html(response && response.name ? `Hi, ${response.name}` : 'Hi, Player');

        if (response && response.clan && response.clan.badge && response.clan.name) {
            $('.info .list.clan').html(`<img src="${response.clan.badge}" alt="Clan Badge"> ${response.clan.name}`);
        } else {
            $('.info .list.clan').html(`<img src="img/default_clan_badge.png" alt="Clan Badge"> N/A`);
        }

        if (response && typeof response.expLevel !== 'undefined') {
            $('.info .list.exp').html(`<img src="img/exp.webp" alt="Experience"> ${response.expLevel}`);
        } else {
            $('.info .list.exp').html(`<img src="img/exp.webp" alt="Experience"> N/A`);
        }

        if (response && typeof response.townHallLevel !== 'undefined' && response.townHallLevel > 0) {
            const townHallImageUrl = `img/th/th${response.townHallLevel}.webp`;
            $('.info .list.th').html(`<img src="${townHallImageUrl}" alt="Town Hall ${response.townHallLevel}"> TH ${response.townHallLevel}`);
        } else {
            $('.info .list.th').html(`<img src="img/th/th_default.webp" alt="Town Hall"> N/A`);
        }

        if (response && typeof response.trophies !== 'undefined') {
            $('.info .list.trophy').html(`<img src="img/trophy.png" alt="Trophies"> ${response.trophies}`);
        } else {
            $('.info .list.trophy').html(`<img src="img/trophy.png" alt="Trophies"> N/A`);
        }
    });


});